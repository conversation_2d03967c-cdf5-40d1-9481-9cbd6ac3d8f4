#include "DruidsSageChatView.h"

#include <Dom/JsonObject.h>
#include <Serialization/JsonWriter.h>
#include <Serialization/JsonReader.h>
#include <Serialization/JsonSerializer.h>
#include <Misc/FileHelper.h>
#include <HAL/FileManager.h>

#include <Widgets/Layout/SScrollBox.h>
#include <Framework/Application/SlateApplication.h>
#include <Input/Events.h>
#include <Engine/Engine.h>
#include <Engine/World.h>
#include <Components/PanelWidget.h>

#include "LogDruids.h"
#include "DruidsSageHelper.h"
#include "IChatRequestHandler.h"
#include "SDruidsSageSimpleChatItem.h"
#include "SDruidsSageAssistantChatItem.h"
#include "ISageExtensionDelegator.h"
#include "DruidsSageMultiLineTextInput.h"
#include "ContextChipWidget.h"

UDruidsSageChatView::UDruidsSageChatView(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer)
	, BPContextChipWidget(nullptr)
	, TabContextChipWidget(nullptr)
	, InputTextBox(nullptr)
	, ChatContentPanel(nullptr)
	, CurrentTabContext(TEXT(""))
	, CurrentTabContextDisplayMessage(TEXT(""))
	, CurrentBPContext(TEXT(""))
	, CurrentBPContextDisplayMessage(TEXT(""))
{
}

void UDruidsSageChatView::NativePreConstruct()
{
	Super::NativePreConstruct();
}

void UDruidsSageChatView::NativeConstruct()
{
	Super::NativeConstruct();

	if (IsDesignTime())
	{
		return;
	}

	// Initialize chat content area
	InitializeChatContent();

	// Bind the Enter key event from the input text box
	if (InputTextBox)
	{
		InputTextBox->OnEnterPressed.AddDynamic(this, &UDruidsSageChatView::HandleEnterPressed);
	}

	// Load chat history
	LoadChatHistory();

	// Set focus to the input text box after a short delay to ensure window is fully constructed
	if (GWorld)
	{
		GWorld->GetTimerManager().SetTimerForNextTick([this]()
		{
			SetInputFocus();
		});
	}
}



void UDruidsSageChatView::SynchronizeProperties()
{
	Super::SynchronizeProperties();
}

void UDruidsSageChatView::ReleaseSlateResources(bool bReleaseChildren)
{
	Super::ReleaseSlateResources(bReleaseChildren);

	if (IsDesignTime())
	{
		return;
	}

	SaveChatHistory();
	ClearChat();
	ChatItems.Empty();

	OnMessageSending.Clear();
}

bool UDruidsSageChatView::IsSendMessageEnabled() const
{
	const bool bNoActiveRequest = ChatRequestHandler.IsValid() && ChatRequestHandler.Get()->IsNoActiveRequest();
	return bNoActiveRequest && InputTextBox && !InputTextBox->IsEmpty();
}

bool UDruidsSageChatView::IsClearChatEnabled() const
{
	return !ChatItems.IsEmpty();
}

void UDruidsSageChatView::ClearChat()
{
	// First stop any ongoing request
	if (ChatRequestHandler.IsValid())
	{
		ChatRequestHandler.Get()->StopAndCleanupRequest(ChatItems);
	}

	// Clear all chat items
	ChatItems.Empty();

	// Clear the chat box if it exists
	if (ChatBox.IsValid())
	{
		ChatBox->ClearChildren();
	}

	// Update context chip visibility
	if (BPContextChipWidget)
	{
		BPContextChipWidget->SetVisibility(CurrentBPContextDisplayMessage.IsEmpty() ? ESlateVisibility::Collapsed : ESlateVisibility::Visible);
	}
}

void UDruidsSageChatView::SetTabContext(const FString& Context, const FString& ContextDisplayMessage)
{
	CurrentTabContext = Context;
	CurrentTabContextDisplayMessage = ContextDisplayMessage;

	FString TruncatedMessage = TruncateString(ContextDisplayMessage);

	if (TabContextChipWidget)
	{
		TabContextChipWidget->SetChipText(TruncatedMessage);
		TabContextChipWidget->SetVisibility(TruncatedMessage.IsEmpty() ? ESlateVisibility::Collapsed : ESlateVisibility::Visible);
	}
}

void UDruidsSageChatView::SetBPContext(const FString& BPContext, const FString& BPContextDisplayMessage)
{
	CurrentBPContext = BPContext;
	CurrentBPContextDisplayMessage = BPContextDisplayMessage;

	FString TruncatedMessage = TruncateString(BPContextDisplayMessage);

	if (BPContextChipWidget)
	{
		BPContextChipWidget->SetChipText(TruncatedMessage);
		BPContextChipWidget->SetVisibility(TruncatedMessage.IsEmpty() ? ESlateVisibility::Collapsed : ESlateVisibility::Visible);
	}
}

void UDruidsSageChatView::SetActiveObject(const TWeakObjectPtr<>& NewActiveObject)
{
	ActiveObject = NewActiveObject;
	UE_LOG(LogDruidsSage_Internal, Display, TEXT("Active Object Set: %s"), *GetNameSafe(NewActiveObject.Get()));
}

void UDruidsSageChatView::SetActiveExtensionDefinitions(
	const TArray<TSharedPtr<FDruidsSageExtensionDefinition>>& NewActiveExtensions)
{
	ActiveExtensionDefinitions = NewActiveExtensions;
}

void UDruidsSageChatView::SetChatRequestHandler(const TWeakPtr<IChatRequestHandler>& NewChatRequestHandler)
{
	ChatRequestHandler = NewChatRequestHandler.Pin().ToSharedRef();
}

void UDruidsSageChatView::SetExtensionsDelegator(const TWeakPtr<ISageExtensionDelegator>& NewExtensionDelegator)
{
	ExtensionDelegator = NewExtensionDelegator.Pin();
}

void UDruidsSageChatView::SetInputFocus()
{
	if (InputTextBox)
	{
		InputTextBox->SetInputFocus();
	}
}

void UDruidsSageChatView::HandleEnterPressed()
{
	if (IsSendMessageEnabled())
	{
		HandleSendMessage(EDruidsSageChatRole::User);
	}
}

FString UDruidsSageChatView::TruncateString(const FString& Input, int32 MaxLength /*= 100*/) const
{
	if (Input.Len() > MaxLength)
	{
		return Input.Left(MaxLength - 3) + TEXT("...");
	}
	return Input;
}

TSharedPtr<IDruidsSageChatItem> UDruidsSageChatView::CreateChatItem(
	EDruidsSageChatRole Role,
	const FString& ChatText) const
{
	if (Role == EDruidsSageChatRole::Assistant)
	{
		TSharedPtr<SDruidsSageAssistantChatItem> AssistantChatItem = SNew(SDruidsSageAssistantChatItem)
			.OnActionApplied(SDruidsSageAssistantChatItem::FOnActionApplied::CreateUObject(
				this, &UDruidsSageChatView::OnActionRequestApplied));

		AssistantChatItem.Get()->SetRawText(ChatText);

		return AssistantChatItem;
	}

	return SNew(SDruidsSageSimpleChatItem)
		.MessageRole(Role)
		.ChatText(ChatText)
		.ScrollBox(ChatScrollBox)
		.ActiveExtensionDefinitions(ActiveExtensionDefinitions);
}

void UDruidsSageChatView::OnActionRequestApplied(const TSharedPtr<FJsonValue>& ActionDetails) const
{
	if (ExtensionDelegator.IsValid())
	{
		ExtensionDelegator.Get()->OnActionApplied(ActionDetails);
	}
}

void UDruidsSageChatView::HandleSendMessage(const EDruidsSageChatRole Role)
{
	// Broadcast that we're about to send a message
	OnMessageSending.Broadcast();

	FString ChatText = InputTextBox ? InputTextBox->GetText().ToString() : FString();
	const TSharedPtr<IDruidsSageChatItem> NewUserChatItem = CreateChatItem(Role, ChatText);

	// Add to chat box if available
	if (ChatBox.IsValid())
	{
		ChatBox->AddSlot().AutoHeight()[NewUserChatItem.ToSharedRef()];
	}
	ChatItems.Add(NewUserChatItem);

	if (Role == EDruidsSageChatRole::System)
	{
		if (ChatScrollBox.IsValid())
		{
			ChatScrollBox->ScrollToEnd();
		}
		if (InputTextBox)
		{
			InputTextBox->SetText(FText::GetEmpty());
		}
		return;
	}

	const TSharedPtr<IDruidsSageChatItem> AssistantMessage = CreateChatItem(
		EDruidsSageChatRole::Assistant,
		FString()
	);

	// *** Combine Contexts for the AI Request ***
	// Simple concatenation for now. Might need more sophisticated formatting later.
	FString CombinedContext = "";
	if (!CurrentTabContext.IsEmpty())
	{
		CombinedContext += CurrentTabContext;
	}
	if (!CurrentBPContext.IsEmpty())
	{
		if (!CombinedContext.IsEmpty())
		{
			CombinedContext += "\n\n"; // Separator
		}
		CombinedContext += "Blueprint Context (current BP nodes selected):\n" + CurrentBPContext;
	}
	// ********************************************

	if (ChatRequestHandler.IsValid())
	{
		ChatRequestHandler.Get()->SetupAndSendRequest(ChatItems, AssistantMessage, CombinedContext);
	}

	// Add to chat box if available
	if (ChatBox.IsValid())
	{
		ChatBox->AddSlot().AutoHeight()[AssistantMessage.ToSharedRef()];
	}
	ChatItems.Add(AssistantMessage);

	if (ChatScrollBox.IsValid())
	{
		ChatScrollBox->ScrollToEnd();
	}
	if (InputTextBox)
	{
		InputTextBox->SetText(FText::GetEmpty());
	}
}

void UDruidsSageChatView::HandleClearChat()
{
	ClearChat();
}

TArray<FDruidsSageChatMessage> UDruidsSageChatView::GetChatHistory() const
{
	TArray<FDruidsSageChatMessage> Output;

	for (const TSharedPtr<IDruidsSageChatItem>& Item : ChatItems)
	{
		FDruidsSageChatMessage DruidsMessage;
		Item->FillInDruidsMessage(DruidsMessage);
		Output.Add(DruidsMessage);
	}

	return Output;
}

FString UDruidsSageChatView::GetHistoryPath() const
{
	return FPaths::Combine(FPaths::ProjectSavedDir(), TEXT("DruidsSage"), TEXT("ChatHistory.json"));
}

void UDruidsSageChatView::LoadChatHistory()
{
	ChatItems.Empty();
	if (ChatBox.IsValid())
	{
		ChatBox->ClearChildren();
	}

	SetTabContext(TEXT(""), TEXT(""));
	SetBPContext(TEXT(""), TEXT(""));

	if (const FString LoadPath = GetHistoryPath(); FPaths::FileExists(LoadPath))
	{
		FString FileContent;
		if (!FFileHelper::LoadFileToString(FileContent, *LoadPath))
		{
			return;
		}

		const TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(FileContent);
		TSharedPtr<FJsonObject> JsonObject;

		if (FJsonSerializer::Deserialize(Reader, JsonObject) && JsonObject.IsValid())
		{
			const TArray<TSharedPtr<FJsonValue>>* DataArray;
			if (JsonObject->TryGetArrayField(TEXT("Data"), DataArray))
			{
				for (const TSharedPtr<FJsonValue>& MessageValue : *DataArray)
				{
					if (const TSharedPtr<FJsonObject> MessageItObj = MessageValue->AsObject())
					{
						EDruidsSageChatRole Role = EDruidsSageChatRole::User;
						if (FString RoleString; MessageItObj->TryGetStringField(TEXT("role"), RoleString))
						{
							Role = UDruidsSageHelper::NameToRole(FName(*RoleString));
						}

						if (FString ContentString; MessageItObj->TryGetStringField(TEXT("content"), ContentString))
						{
							ChatItems.Emplace(CreateChatItem(Role, ContentString));
						}
						else if (const TArray<TSharedPtr<FJsonValue>>* ContentArray; MessageItObj->TryGetArrayField(TEXT("content"), ContentArray))
						{
							for (TSharedPtr ContentValue : *ContentArray)
							{
								if (TSharedPtr<FJsonObject> ContentObj = ContentValue->AsObject())
								{
									if (FString ContentType;
										ContentObj->TryGetStringField(TEXT("type"), ContentType) &&
										ContentType == TEXT("text"))
									{
										if (FString TextContent;
											ContentObj->TryGetStringField(TEXT("text"), TextContent))
										{
											ChatItems.Emplace(CreateChatItem(Role, TextContent));
										}
									}
								}
							}
						}
					}
				}
			}
		}
	}

	// Add loaded items to the chat box
	if (ChatBox.IsValid())
	{
		for (const TSharedPtr<IDruidsSageChatItem>& Item : ChatItems)
		{
			ChatBox->AddSlot().AutoHeight()[Item.ToSharedRef()];
		}
	}
}

void UDruidsSageChatView::SaveChatHistory() const
{
	const TSharedPtr<FJsonObject> JsonRequest = MakeShared<FJsonObject>();

	TArray<TSharedPtr<FJsonValue>> Data;
	for (const FDruidsSageChatMessage& Item : GetChatHistory())
	{
		Data.Add(Item.GetMessageJson());
	}

	if (!Data.IsEmpty())
	{
		JsonRequest->SetArrayField("Data", Data);
	}

	FString RequestContentString;
	const TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&RequestContentString);

	if (FJsonSerializer::Serialize(JsonRequest.ToSharedRef(), Writer))
	{
		// Ensure the directory exists
		const FString DirectoryPath = FPaths::GetPath(GetHistoryPath());
		if (!FPaths::DirectoryExists(DirectoryPath))
		{
			IFileManager::Get().MakeDirectory(*DirectoryPath, true);
		}

		FFileHelper::SaveStringToFile(RequestContentString, *GetHistoryPath());
	}
}

void UDruidsSageChatView::InitializeChatContent()
{
	// If we have a UMG panel for chat content, we don't need to create Slate widgets
	if (ChatContentPanel)
	{
		// The chat content will be managed by UMG widgets in the Blueprint
		// We still need to create the Slate widgets for chat items since they haven't been converted to UMG yet
		ChatScrollBox = SNew(SScrollBox);
		ChatBox = SNew(SVerticalBox);
		ChatScrollBox->AddSlot()[ChatBox.ToSharedRef()];
		return;
	}

	// If no UMG panel is bound, create Slate widgets as before (for backwards compatibility)
	constexpr float SlotPadding = 4.0f;

	TSharedRef<SWidget> ChatContent = SNew(SVerticalBox)
		+ SVerticalBox::Slot().Padding(SlotPadding).FillHeight(1.f)
		[
			SAssignNew(ChatScrollBox, SScrollBox)
			+ SScrollBox::Slot()
			[
				SAssignNew(ChatBox, SVerticalBox)
			]
		]
		// Context chips area - managed by UMG
		+ SVerticalBox::Slot().Padding(SlotPadding).AutoHeight()
		[
			SNew(SBox)
			.HAlign(HAlign_Fill)
			[
				SNew(STextBlock)
				.Text(FText::FromString(TEXT("Context chips managed by UMG")))
			]
		]
		// Text input area - managed by UMG
		+ SVerticalBox::Slot().Padding(SlotPadding).AutoHeight()
		[
			SNew(SBox)
			.HAlign(HAlign_Fill)
			[
				SNew(STextBlock)
				.Text(FText::FromString(TEXT("Text input managed by UMG")))
			]
		];

	// Add the Slate widget to the root widget (this is a fallback for when not using UMG properly)
	// Note: This approach is not recommended for UMG widgets and should be avoided
}
